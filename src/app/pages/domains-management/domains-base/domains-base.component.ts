import { Directive, Input, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { RowAction, SettingsService, SwuiGridComponent, SwuiGridField, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil, tap } from 'rxjs/operators';
import { DEFAULT_PAGE_SIZE } from '../../../app.constants';
import { Domain, DOMAIN_TYPES, DomainType, LobbyDomain, StaticDomain, DynamicDomain } from '../../../common/models/domain.model';
import { ConfirmationDialogComponent } from '../confirmation-dialog/confirmation-dialog.component';
import { DomainsItemDialogComponent } from '../domains-item-dialog/domains-item-dialog.component';
import { DomainsManagementService } from '../domains-management.service';

@Directive()
export abstract class DomainsBaseComponent implements OnInit, OnDestroy {
  @Input() gameServers: string[] = [];

  schema: SwuiGridField[];
  rowActions: RowAction[] = [];
  data: Domain[] | LobbyDomain[] | StaticDomain[] | DynamicDomain[];
  domainType = DOMAIN_TYPES.static;
  pageSize = DEFAULT_PAGE_SIZE;

  @ViewChild('grid', {static: true}) public grid: SwuiGridComponent<Domain>;

  protected _destroyed$ = new Subject();

  protected constructor(protected service: DomainsManagementService,
                        private settingsService: SettingsService,
                        private dialog: MatDialog,
                        protected notifications: SwuiNotificationsService,
                        protected translate: TranslateService
  ) {
    this.service.isGridChanged$
      .pipe(
        filter((val: DomainType) => val === this.domainType),
        switchMap(() => this.service.getList(this.domainType, undefined, true)),
        takeUntil(this._destroyed$)
      )
      .subscribe((val: Domain[]) => this.data = val);
  }

  ngOnInit() {
    this.settingsService.appSettings$
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(val => this.pageSize = val.pageSize);

    this.service.getList(this.domainType)
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe((val: Domain[]) => {
        this.data = val;
      });
    this.setRowActions();

  }

  refreshGrid() {
    this.service.isGridChanged$.next(this.domainType);
  }

  ngOnDestroy() {
    this._destroyed$.next(this.domainType);
    this._destroyed$.complete();
  }

  protected setRowActions() {
    this.rowActions = [
      {
        title: 'DOMAINS.GRID.editDomain',
        icon: 'edit',
        fn: (item: Domain) => {
          const dialogRef = this.dialog.open(DomainsItemDialogComponent, {
            width: '500px',
            data: {
              domain: item,
              gameServers: this.gameServers,
              type: this.domainType
            },
            disableClose: true
          });

          dialogRef.afterClosed()
            .pipe(
              filter(data => !!data),
              switchMap((domain: Domain) => this.service.update(item.id, domain, this.domainType)),
              switchMap((domain: Domain) =>
                this.translate.get('DOMAINS.notificationModified', {domain: domain.domain})),
              tap((message) => this.notifications.success(message)),
              switchMap(() => this.service.getList(this.domainType, undefined, true)),
              takeUntil(this._destroyed$)
            )
            .subscribe((val: Domain[]) => this.data = val);
        },
        canActivateFn: () => true,
      },
      {
        title: 'DOMAINS.GRID.removeDomain',
        icon: 'delete',
        fn: (item: Domain) => {
          const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
            width: '500px',
            data: item,
            disableClose: true
          });

          dialogRef.afterClosed()
            .pipe(
              filter(data => !!data),
              switchMap(() => this.service.delete(item.id, this.domainType)),
              switchMap(() => this.translate.get('DOMAINS.notificationRemoved', {domain: item.domain})),
              tap((message) => this.notifications.success(message)),
              switchMap(() => this.service.getList(this.domainType, undefined, true)),
              takeUntil(this._destroyed$)
            )
            .subscribe((val: Domain[]) => this.data = val);
        },
        canActivateFn: () => true,
      },
    ];
  }
}
