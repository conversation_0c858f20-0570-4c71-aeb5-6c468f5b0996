import { Component, Input, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { RowAction, SettingsService, SwuiGridComponent, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil, tap } from 'rxjs/operators';
import { DynamicDomain, DynamicDomainPool } from '../../../common/models/domain.model';
import { DomainsManagementService } from '../domains-management.service';
import { dynamicDomainPoolsSchema } from './schema';
import { DEFAULT_PAGE_SIZE } from '../../../app.constants';
import { ConfirmationDialogComponent } from '../confirmation-dialog/confirmation-dialog.component';

@Component({
  selector: 'domains-dynamic-pool',
  templateUrl: './domains-dynamic-pool.component.html',
})
export class DomainsDynamicPoolComponent implements OnInit, OnDestroy {
  @Input() dynamicDomains: DynamicDomain[] = [];

  schema = dynamicDomainPoolsSchema;
  pageSize = DEFAULT_PAGE_SIZE;
  rowActions: RowAction[] = [];
  data: DynamicDomainPool[] = [];

  @ViewChild('grid', {static: true}) public grid: SwuiGridComponent<DynamicDomainPool>;

  private readonly _destroyed$ = new Subject();

  constructor(
    private readonly service: DomainsManagementService,
    private readonly settingsService: SettingsService,
    private readonly dialog: MatDialog,
    private readonly notifications: SwuiNotificationsService,
    private readonly translate: TranslateService
  ) {
    this.service.isGridChanged$.pipe(
      switchMap(() => this.service.getDynamicDomainPools(undefined, true)),
      takeUntil(this._destroyed$)
    ).subscribe((val: DynamicDomainPool[]) => {
      this.data = val;
    });
  }

  ngOnInit() {
    this.settingsService.appSettings$.pipe(takeUntil(this._destroyed$)).subscribe(val => {
      this.pageSize = val.pageSize;
    });

    this.service.getDynamicDomainPools().pipe(takeUntil(this._destroyed$)).subscribe((val: DynamicDomainPool[]) => {
      this.data = val;
    });

    this.setRowActions();
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  refreshGrid() {
    this.service.isGridChanged$.next();
  }

  private setRowActions() {
    this.rowActions = [
      {
        title: 'DOMAINS.GRID.editPool',
        icon: 'edit',
        fn: () => {
          // TODO: Implement edit dialog for dynamic domain pools
          this.notifications.success('Dynamic Domain Pool editing will be implemented');
        },
        canActivateFn: () => true,
      },
      {
        title: 'DOMAINS.GRID.removePool',
        icon: 'delete',
        fn: (item: DynamicDomainPool) => {
          const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
            width: '500px',
            data: item,
            disableClose: true
          });

          dialogRef.afterClosed()
            .pipe(
              filter(data => !!data),
              switchMap(() => this.service.deleteDomainPool(item.id, 'dynamic')),
              switchMap(() => this.translate.get('DOMAINS.notificationPoolRemoved', {name: item.name})),
              tap((message) => this.notifications.success(message)),
              switchMap(() => this.service.getDynamicDomainPools(undefined, true)),
              takeUntil(this._destroyed$)
            )
            .subscribe((val: DynamicDomainPool[]) => this.data = val);
        },
        canActivateFn: () => true,
      },
    ];
  }
}
