import { BaseApiObject } from '../typings';

export type StaticDomainType = 'static' | 'lobby' | 'live-streaming' | 'ehub';
export type DomainType = 'dynamic' | StaticDomainType;
export const DOMAIN_TYPES: {
  'static': DomainType,
  'dynamic': DomainType,
  'lobby': DomainType,
  'live-streaming': DomainType,
  'ehub': DomainType
} = {
  'static': 'static',
  'dynamic': 'dynamic',
  'lobby': 'lobby',
  'live-streaming': 'live-streaming',
  'ehub': 'ehub',
};

export type DomainPoolType = 'static' | 'dynamic';
export const DOMAIN_POOL_TYPES: {
  'static': DomainPoolType,
  'dynamic': DomainPoolType
} = {
  'static': 'static',
  'dynamic': 'dynamic',
};

export type DomainStatus = 'active' | 'suspended';
export const DOMAIN_STATUS: {
  'active': DomainStatus,
  'suspended': DomainStatus
} = {
  'active': 'active',
  'suspended': 'suspended',
};

export interface DomainPoolItem {
  id: string;
  isActive?: boolean;
}

export interface DomainPoolRow {
  id: string;
  inherited?: boolean;
  name?: string;
  domains?: DomainPoolItem[];
  lobbyDomains?: DomainPoolItem[];
  createdAt: string;
  updatedAt: string;
}

export type DomainPool = DomainPoolRow & BaseApiObject;

export interface StaticDomainPoolRow extends DomainPoolRow {
  domains?: StaticDomainPoolItem[];
  lobbyDomains?: LobbyDomainPoolItem[];
}

export type StaticDomainPool = StaticDomainPoolRow & BaseApiObject;

export interface DynamicDomainPoolRow extends Omit<DomainPoolRow, 'lobbyDomains'> {
  domains?: DynamicDomainPoolItem[];
}

export type DynamicDomainPool = DynamicDomainPoolRow & BaseApiObject;

export interface StaticDomainPoolItem extends StaticDomain {
  isActive?: boolean;
}

export interface DynamicDomainPoolItem extends DynamicDomain {
  isActive?: boolean;
}

export interface LobbyDomainPoolItem extends LobbyDomain {
  isActive?: boolean;
}

export interface DomainRow {
  id: string;
  name?: string;
  domain: string;
  environment?: string;
  description?: string;
  provider?: string;
  status?: DomainStatus;
  type?: string;
  expiryDate?: string;
  isActive?: boolean;
  createdAt: string;
  updatedAt: string;
}

export type Domain = DomainRow & BaseApiObject;

export interface StaticDomainRow extends DomainRow {
  type: 'static' | 'lobby' | 'live-streaming' | 'ehub';
}

export type StaticDomain = StaticDomainRow & BaseApiObject;

export interface DynamicDomainRow extends DomainRow {
  environment: string;
}

export type DynamicDomain = DynamicDomainRow & BaseApiObject;

export interface LobbyDomainRow extends Omit<DomainRow, 'domain'> {
  name: string;
}

export type LobbyDomain = LobbyDomainRow & BaseApiObject;

export interface DomainsItemDialogData {
  domain?: Domain;
  gameServers?: string[];
  type: DomainType;
}

export interface DomainCreateData {
  domain: string;
  description?: string;
  provider?: string;
  status?: DomainStatus;
  type?: string;
  expiryDate?: string;
  environment?: string;
}

export interface DomainUpdateData extends Partial<DomainCreateData> {
  id: string;
  isActive?: boolean;
}

export interface DomainPoolCreateData {
  name: string;
  domains?: DomainPoolItem[];
}

export interface DomainPoolUpdateData extends Partial<DomainPoolCreateData> {
  id: string;
}

export interface DomainSearchFilters {
  domain?: string;
  description?: string;
  provider?: string;
  status?: DomainStatus;
  type?: string;
}
